import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  Search,
  RefreshCw,
  Eye,
  Truck,
  CheckCircle2,
  XCircle,
  Package,
  Download,
  ArrowUpDown,
  SlidersHorizontal,
  DollarSign,
  TrendingUp,
  Calendar,
  User,
  FileText
} from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import _OrderDetailView from "./OrderDetailView";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { _Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { DateRangePicker } from "@/components/ui/date-range-picker";

// Mock order data
const mockOrders = [
  {
    id: "ORD-001",
    customer: {
      name: "John Doe",
      email: "<EMAIL>",
    },
    date: "2023-05-15",
    status: "processing",
    total: 89.97,
    items: [
      { id: "1", name: "Lemon Verbena Fresh Bar", quantity: 2, price: 14.99 },
      { id: "3", name: "Scalp Rescue Shampoo", quantity: 1, price: 24.99 },
      { id: "7", name: "Herbal Hammam Bar", quantity: 1, price: 34.99 },
    ],
    shippingAddress: {
      line1: "123 Main St",
      line2: "Apt 4B",
      city: "New York",
      state: "NY",
      zipCode: "10001",
      country: "USA",
    },
    paymentMethod: "credit_card",
  },
  {
    id: "ORD-002",
    customer: {
      name: "Jane Smith",
      email: "<EMAIL>",
    },
    date: "2023-05-16",
    status: "shipped",
    total: 49.98,
    items: [
      { id: "2", name: "Rose Clay Glow Bar", quantity: 1, price: 14.99 },
      { id: "5", name: "Shine & Silk Shampoo", quantity: 1, price: 34.99 },
    ],
    shippingAddress: {
      line1: "456 Oak Ave",
      line2: "",
      city: "Los Angeles",
      state: "CA",
      zipCode: "90001",
      country: "USA",
    },
    paymentMethod: "paypal",
  },
  {
    id: "ORD-003",
    customer: {
      name: "Robert Johnson",
      email: "<EMAIL>",
    },
    date: "2023-05-17",
    status: "delivered",
    total: 104.97,
    items: [
      { id: "4", name: "Sensitive Scalp Shampoo", quantity: 1, price: 24.99 },
      { id: "8", name: "Royal Oud Bar", quantity: 2, price: 39.99 },
    ],
    shippingAddress: {
      line1: "789 Pine St",
      line2: "",
      city: "Chicago",
      state: "IL",
      zipCode: "60007",
      country: "USA",
    },
    paymentMethod: "cash_on_delivery",
  },
  {
    id: "ORD-004",
    customer: {
      name: "Emily Davis",
      email: "<EMAIL>",
    },
    date: "2023-05-18",
    status: "pending",
    total: 74.98,
    items: [
      { id: "6", name: "Hair Growth Boost Bar", quantity: 2, price: 37.49 },
    ],
    shippingAddress: {
      line1: "321 Elm St",
      line2: "Suite 101",
      city: "Miami",
      state: "FL",
      zipCode: "33101",
      country: "USA",
    },
    paymentMethod: "credit_card",
  },
  {
    id: "ORD-005",
    customer: {
      name: "Michael Wilson",
      email: "<EMAIL>",
    },
    date: "2023-05-19",
    status: "cancelled",
    total: 89.97,
    items: [
      { id: "9", name: "White Misk Bar", quantity: 3, price: 29.99 },
    ],
    shippingAddress: {
      line1: "654 Maple Ave",
      line2: "",
      city: "Seattle",
      state: "WA",
      zipCode: "98101",
      country: "USA",
    },
    paymentMethod: "paypal",
  },
];

const OrderManagement = () => {
  const [orders, setOrders] = useState(mockOrders);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPaymentMethod, setFilterPaymentMethod] = useState("all");
  const [_selectedOrder, _setSelectedOrder] = useState(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [newStatus, setNewStatus] = useState("");
  const [_showDetailDialog, _setShowDetailDialog] = useState(false);
  const [sortField, setSortField] = useState("date");
  const [sortDirection, setSortDirection] = useState("desc");
  const [dateRange, setDateRange] = useState(undefined);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [totalAmount, setTotalAmount] = useState(0);
  const [orderCount, setOrderCount] = useState(0);
  const _isMobile = useIsMobile();

  // Filter and search orders
  const filteredOrders = orders.filter(order => {
    // Search term filter
    const matchesSearch =
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.email.toLowerCase().includes(searchTerm.toLowerCase());

    // Status filter
    const matchesStatus = filterStatus === "all" || order.status === filterStatus;

    // Payment method filter
    const matchesPaymentMethod = filterPaymentMethod === "all" || order.paymentMethod === filterPaymentMethod;

    // Date range filter
    let matchesDateRange = true;
    if (dateRange && dateRange.from) {
      const orderDate = new Date(order.date);
      const fromDate = new Date(dateRange.from);
      fromDate.setHours(0, 0, 0, 0);

      if (dateRange.to) {
        const toDate = new Date(dateRange.to);
        toDate.setHours(23, 59, 59, 999);
        matchesDateRange = orderDate >= fromDate && orderDate <= toDate;
      } else {
        matchesDateRange = orderDate.toDateString() === fromDate.toDateString();
      }
    }

    return matchesSearch && matchesStatus && matchesPaymentMethod && matchesDateRange;
  });

  // Simulate loading orders
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  }, []);

  // Calculate order statistics
  useEffect(() => {
    if (filteredOrders.length > 0) {
      const total = filteredOrders.reduce((sum, order) => sum + order.total, 0);
      setTotalAmount(total);
      setOrderCount(filteredOrders.length);
    } else {
      setTotalAmount(0);
      setOrderCount(0);
    }
  }, [filteredOrders]);

  // Handle order status update
  const handleUpdateStatus = (orderId, status) => {
    setIsUpdatingStatus(true);

    // Simulate API call
    setTimeout(() => {
      setOrders(prev =>
        prev.map(order =>
          order.id === orderId ? { ...order, status } : order
        )
      );

      toast.success(`Order ${orderId} status updated to ${status}`);
      setIsUpdatingStatus(false);
      _setSelectedOrder(null);
      setNewStatus("");
    }, 1000);
  };

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setSearchTerm("");
    setFilterStatus("all");
    setFilterPaymentMethod("all");
    setDateRange(undefined);
    setShowAdvancedFilters(false);
  };



  // Sort orders
  const _sortedOrders = [...filteredOrders].sort((a, b) => {
    let aValue, bValue;

    // Handle different field types
    switch (sortField) {
      case "id":
        aValue = a.id;
        bValue = b.id;
        break;
      case "customer":
        aValue = a.customer.name;
        bValue = b.customer.name;
        break;
      case "date":
        aValue = new Date(a.date);
        bValue = new Date(b.date);
        break;
      case "total":
        aValue = a.total;
        bValue = b.total;
        break;
      case "status":
        aValue = a.status;
        bValue = b.status;
        break;
      default:
        aValue = a[sortField];
        bValue = b[sortField];
    }

    // Sort direction
    if (sortDirection === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Get status badge
  const getStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="border-yellow-600 text-yellow-400">Pending</Badge>;
      case "processing":
        return <Badge variant="outline" className="border-blue-600 text-blue-400">Processing</Badge>;
      case "shipped":
        return <Badge variant="outline" className="border-purple-600 text-purple-400">Shipped</Badge>;
      case "delivered":
        return <Badge variant="outline" className="border-green-600 text-green-400">Delivered</Badge>;
      case "cancelled":
        return <Badge variant="destructive" className="bg-red-900/60 hover:bg-red-900/80 text-red-100">Cancelled</Badge>;
      default:
        return <Badge variant="outline" className="border-sabone-gold/30 text-sabone-cream">Unknown</Badge>;
    }
  };

  // Get payment method display name
  const _getPaymentMethodName = (method) => {
    switch (method) {
      case "credit_card":
        return "Credit Card";
      case "paypal":
        return "PayPal";
      case "cash_on_delivery":
        return "Cash on Delivery";
      default:
        return method;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Order Management</h2>
          <p className="text-sabone-cream/70 mt-1">View and manage customer orders</p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={() => {
              setLoading(true);
              setTimeout(() => setLoading(false), 1000);
            }}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Order Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-4 flex justify-between items-center">
            <div>
              <p className="text-sabone-cream/70 text-sm">Total Orders</p>
              <p className="text-2xl font-bold text-sabone-gold">{orderCount}</p>
            </div>
            <div className="h-10 w-10 rounded-full bg-sabone-gold/10 flex items-center justify-center">
              <Package className="h-5 w-5 text-sabone-gold" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-4 flex justify-between items-center">
            <div>
              <p className="text-sabone-cream/70 text-sm">Total Revenue</p>
              <p className="text-2xl font-bold text-sabone-gold">${totalAmount.toFixed(2)}</p>
            </div>
            <div className="h-10 w-10 rounded-full bg-sabone-gold/10 flex items-center justify-center">
              <DollarSign className="h-5 w-5 text-sabone-gold" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardContent className="p-4 flex justify-between items-center">
            <div>
              <p className="text-sabone-cream/70 text-sm">Average Order Value</p>
              <p className="text-2xl font-bold text-sabone-gold">
                ${orderCount > 0 ? (totalAmount / orderCount).toFixed(2) : '0.00'}
              </p>
            </div>
            <div className="h-10 w-10 rounded-full bg-sabone-gold/10 flex items-center justify-center">
              <TrendingUp className="h-5 w-5 text-sabone-gold" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sabone-cream/50 h-4 w-4" />
          <Input
            placeholder="Search orders by ID, customer name, or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
          />
        </div>

        <div className="flex items-center gap-2">
          <Popover open={showAdvancedFilters} onOpenChange={setShowAdvancedFilters}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
              >
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 bg-sabone-dark-olive border-sabone-gold/30 p-4">
              <div className="space-y-4">
                <h4 className="font-medium text-sabone-gold">Filter Orders</h4>

                <div className="space-y-2">
                  <Label htmlFor="status-filter" className="text-sabone-cream">Status</Label>
                  <Select id="status-filter" value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-full bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent className="bg-sabone-dark-olive border-sabone-gold/30">
                      <SelectItem value="all" className="text-sabone-cream">All Orders</SelectItem>
                      <SelectItem value="pending" className="text-sabone-cream">Pending</SelectItem>
                      <SelectItem value="processing" className="text-sabone-cream">Processing</SelectItem>
                      <SelectItem value="shipped" className="text-sabone-cream">Shipped</SelectItem>
                      <SelectItem value="delivered" className="text-sabone-cream">Delivered</SelectItem>
                      <SelectItem value="cancelled" className="text-sabone-cream">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="payment-filter" className="text-sabone-cream">Payment Method</Label>
                  <Select id="payment-filter" value={filterPaymentMethod} onValueChange={setFilterPaymentMethod}>
                    <SelectTrigger className="w-full bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream">
                      <SelectValue placeholder="Filter by payment" />
                    </SelectTrigger>
                    <SelectContent className="bg-sabone-dark-olive border-sabone-gold/30">
                      <SelectItem value="all" className="text-sabone-cream">All Methods</SelectItem>
                      <SelectItem value="credit_card" className="text-sabone-cream">Credit Card</SelectItem>
                      <SelectItem value="paypal" className="text-sabone-cream">PayPal</SelectItem>
                      <SelectItem value="cash_on_delivery" className="text-sabone-cream">Cash on Delivery</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sabone-cream">Date Range</Label>
                  <DateRangePicker value={dateRange} onChange={setDateRange} />
                </div>

                <div className="flex justify-between pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    onClick={resetFilters}
                  >
                    Reset
                  </Button>
                  <Button
                    size="sm"
                    className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
                    onClick={() => setShowAdvancedFilters(false)}
                  >
                    Apply Filters
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <Separator className="bg-sabone-gold/20" />

      <div className="rounded-md border border-sabone-gold/20 overflow-hidden">
        <Table>
          <TableHeader className="bg-sabone-dark-olive/60">
            <TableRow>
              <TableHead
                className="text-sabone-gold cursor-pointer hover:text-sabone-gold/80"
                onClick={() => handleSort("id")}
              >
                <div className="flex items-center">
                  Order ID
                  {sortField === "id" && (
                    <ArrowUpDown className={`ml-1 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="text-sabone-gold cursor-pointer hover:text-sabone-gold/80"
                onClick={() => handleSort("customer")}
              >
                <div className="flex items-center">
                  Customer
                  {sortField === "customer" && (
                    <ArrowUpDown className={`ml-1 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="text-sabone-gold cursor-pointer hover:text-sabone-gold/80"
                onClick={() => handleSort("date")}
              >
                <div className="flex items-center">
                  Date
                  {sortField === "date" && (
                    <ArrowUpDown className={`ml-1 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="text-sabone-gold cursor-pointer hover:text-sabone-gold/80 text-right"
                onClick={() => handleSort("total")}
              >
                <div className="flex items-center justify-end">
                  Total
                  {sortField === "total" && (
                    <ArrowUpDown className={`ml-1 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                  )}
                </div>
              </TableHead>
              <TableHead
                className="text-sabone-gold cursor-pointer hover:text-sabone-gold/80 text-center"
                onClick={() => handleSort("status")}
              >
                <div className="flex items-center justify-center">
                  Status
                  {sortField === "status" && (
                    <ArrowUpDown className={`ml-1 h-4 w-4 ${sortDirection === "asc" ? "rotate-180" : ""}`} />
                  )}
                </div>
              </TableHead>
              <TableHead className="text-sabone-gold text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              Array(5).fill(0).map((_, index) => (
                <TableRow key={index}>
                  <TableCell colSpan={6} className="py-4">
                    <div className="h-6 bg-sabone-dark-olive/40 rounded animate-pulse"></div>
                  </TableCell>
                </TableRow>
              ))
            ) : filteredOrders.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-sabone-cream/70">
                  No orders found matching your criteria
                </TableCell>
              </TableRow>
            ) : (
              filteredOrders.map((order) => (
                <TableRow key={order.id} className="hover:bg-sabone-dark-olive/30">
                  <TableCell className="font-medium text-sabone-gold">{order.id}</TableCell>
                  <TableCell>
                    <div className="text-sabone-cream">{order.customer.name}</div>
                    <div className="text-sabone-cream/70 text-sm">{order.customer.email}</div>
                  </TableCell>
                  <TableCell className="text-sabone-cream">{order.date}</TableCell>
                  <TableCell className="text-sabone-cream text-right">${order.total.toFixed(2)}</TableCell>
                  <TableCell className="text-center">
                    {getStatusBadge(order.status)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                            onClick={() => setSelectedOrder(order)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="bg-sabone-dark-olive border-sabone-gold/30 text-sabone-cream max-w-3xl">
                          <DialogHeader>
                            <DialogTitle className="text-sabone-gold">Order Details - {order.id}</DialogTitle>
                          </DialogHeader>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                            <div className="space-y-4">
                              <div className="flex items-center">
                                <Calendar className="h-5 w-5 text-sabone-gold mr-2" />
                                <h3 className="text-sabone-gold font-medium">Order Information</h3>
                              </div>

                              <div className="bg-sabone-charcoal/30 p-4 rounded-md space-y-2">
                                <div className="flex justify-between">
                                  <span className="text-sabone-cream/70">Order ID:</span>
                                  <span className="text-sabone-cream">{order.id}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sabone-cream/70">Date:</span>
                                  <span className="text-sabone-cream">{order.date}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sabone-cream/70">Status:</span>
                                  <span>{getStatusBadge(order.status)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sabone-cream/70">Payment Method:</span>
                                  <span className="text-sabone-cream capitalize">{order.paymentMethod.replace('_', ' ')}</span>
                                </div>
                              </div>

                              <div className="flex items-center">
                                <User className="h-5 w-5 text-sabone-gold mr-2" />
                                <h3 className="text-sabone-gold font-medium">Customer Information</h3>
                              </div>

                              <div className="bg-sabone-charcoal/30 p-4 rounded-md space-y-2">
                                <div className="flex justify-between">
                                  <span className="text-sabone-cream/70">Name:</span>
                                  <span className="text-sabone-cream">{order.customer.name}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sabone-cream/70">Email:</span>
                                  <span className="text-sabone-cream">{order.customer.email}</span>
                                </div>
                              </div>

                              <div className="flex items-center">
                                <Truck className="h-5 w-5 text-sabone-gold mr-2" />
                                <h3 className="text-sabone-gold font-medium">Shipping Address</h3>
                              </div>

                              <div className="bg-sabone-charcoal/30 p-4 rounded-md">
                                <p className="text-sabone-cream">{order.shippingAddress.line1}</p>
                                {order.shippingAddress.line2 && (
                                  <p className="text-sabone-cream">{order.shippingAddress.line2}</p>
                                )}
                                <p className="text-sabone-cream">
                                  {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
                                </p>
                                <p className="text-sabone-cream">{order.shippingAddress.country}</p>
                              </div>
                            </div>

                            <div className="space-y-4">
                              <div className="flex items-center">
                                <Package className="h-5 w-5 text-sabone-gold mr-2" />
                                <h3 className="text-sabone-gold font-medium">Order Items</h3>
                              </div>

                              <div className="bg-sabone-charcoal/30 p-4 rounded-md">
                                <div className="space-y-3">
                                  {order.items.map((item) => (
                                    <div key={item.id} className="flex justify-between items-center border-b border-sabone-gold/10 pb-2 last:border-0 last:pb-0">
                                      <div>
                                        <p className="text-sabone-cream">{item.name}</p>
                                        <p className="text-sabone-cream/70 text-sm">Qty: {item.quantity}</p>
                                      </div>
                                      <p className="text-sabone-gold">${(item.price * item.quantity).toFixed(2)}</p>
                                    </div>
                                  ))}
                                </div>

                                <div className="mt-4 pt-4 border-t border-sabone-gold/20">
                                  <div className="flex justify-between font-medium">
                                    <span className="text-sabone-cream">Total:</span>
                                    <span className="text-sabone-gold">${order.total.toFixed(2)}</span>
                                  </div>
                                </div>
                              </div>

                              <div className="flex items-center">
                                <FileText className="h-5 w-5 text-sabone-gold mr-2" />
                                <h3 className="text-sabone-gold font-medium">Update Order Status</h3>
                              </div>

                              <div className="bg-sabone-charcoal/30 p-4 rounded-md space-y-4">
                                <Select defaultValue={order.status} onValueChange={setNewStatus}>
                                  <SelectTrigger className="w-full bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream">
                                    <SelectValue placeholder="Select new status" />
                                  </SelectTrigger>
                                  <SelectContent className="bg-sabone-dark-olive border-sabone-gold/30">
                                    <SelectItem value="pending" className="text-sabone-cream">Pending</SelectItem>
                                    <SelectItem value="processing" className="text-sabone-cream">Processing</SelectItem>
                                    <SelectItem value="shipped" className="text-sabone-cream">Shipped</SelectItem>
                                    <SelectItem value="delivered" className="text-sabone-cream">Delivered</SelectItem>
                                    <SelectItem value="cancelled" className="text-sabone-cream">Cancelled</SelectItem>
                                  </SelectContent>
                                </Select>

                                <Button
                                  onClick={() => handleUpdateStatus(order.id, newStatus || order.status)}
                                  className="w-full bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
                                  disabled={isUpdatingStatus || !newStatus || newStatus === order.status}
                                >
                                  {isUpdatingStatus ? (
                                    <>
                                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                      Updating...
                                    </>
                                  ) : (
                                    <>
                                      <CheckCircle2 className="h-4 w-4 mr-2" />
                                      Update Status
                                    </>
                                  )}
                                </Button>
                              </div>
                            </div>
                          </div>

                          <DialogFooter className="mt-6">
                            <DialogClose asChild>
                              <Button variant="outline" className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10">
                                Close
                              </Button>
                            </DialogClose>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>

                      <Button
                        variant="outline"
                        size="sm"
                        className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                        onClick={() => {
                          setSelectedOrder(order);
                          setNewStatus(order.status === "pending" ? "processing" : order.status === "processing" ? "shipped" : order.status === "shipped" ? "delivered" : order.status);
                          handleUpdateStatus(order.id, newStatus || (order.status === "pending" ? "processing" : order.status === "processing" ? "shipped" : order.status === "shipped" ? "delivered" : order.status));
                        }}
                        disabled={order.status === "delivered" || order.status === "cancelled"}
                      >
                        {order.status === "pending" ? (
                          <>
                            <CheckCircle2 className="h-4 w-4 mr-1" />
                            Process
                          </>
                        ) : order.status === "processing" ? (
                          <>
                            <Truck className="h-4 w-4 mr-1" />
                            Ship
                          </>
                        ) : order.status === "shipped" ? (
                          <>
                            <CheckCircle2 className="h-4 w-4 mr-1" />
                            Deliver
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </>
                        )}
                      </Button>

                      {order.status !== "cancelled" && order.status !== "delivered" && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                          onClick={() => {
                            setSelectedOrder(order);
                            setNewStatus("cancelled");
                            handleUpdateStatus(order.id, "cancelled");
                          }}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Cancel
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default OrderManagement;
