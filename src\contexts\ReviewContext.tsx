import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  Review,
  ReviewFilterOptions,
  ReviewSummary,
  ReviewStatus,
  ReviewAnalytics
} from '@/types/review';
import {
  getProductReviews,
  getFilteredReviews,
  getReviewSummary,
  addReview as addReviewService,
  updateReview as updateReviewService,
  deleteReview as deleteReviewService,
  hasUserPurchasedProduct,
  addAdminResponse as addAdminResponseService,
  getPendingReviews as getPendingReviewsService
} from '@/services/reviewService';
import {
  analyzeSentiment,
  moderateContent,
  getReviewAnalytics
} from '@/services/reviewAnalyticsService';
import {
  voteOnReview,
  getReviewVoteCounts,
  getUserVoteForReview,
  sortReviewsByHelpfulness
} from '@/services/reviewVotingService';
import { useAuth } from '@/contexts/AuthContext';
import { logger } from '@/utils/logger';

interface ReviewContextType {
  // Review data
  productReviews: Record<string, Review[]>;
  reviewSummaries: Record<string, ReviewSummary>;
  pendingReviews: Review[];

  // Loading states
  isLoadingReviews: boolean;
  isLoadingSummary: boolean;
  isLoadingPendingReviews: boolean;

  // Review actions
  getReviews: (productId: string, status?: ReviewStatus | 'all') => Promise<Review[]>;
  getFilteredProductReviews: (productId: string, options: ReviewFilterOptions) => Promise<Review[]>;
  getSummary: (productId: string) => Promise<ReviewSummary>;
  submitReview: (review: Omit<Review, 'id' | 'userId' | 'userName' | 'createdAt' | 'updatedAt' | 'isVerifiedPurchase' | 'status'>) => Promise<Review>;
  updateReview: (reviewId: string, updates: Partial<Review>) => Promise<Review>;
  deleteReview: (reviewId: string) => Promise<boolean>;
  addAdminResponse: (reviewId: string, response: string) => Promise<Review>;
  getPendingReviews: () => Promise<Review[]>;
  refreshReviews: (productId: string) => Promise<void>;
  refreshPendingReviews: () => Promise<void>;

  // Enhanced features
  voteOnReview: (reviewId: string, voteType: 'helpful' | 'unhelpful') => Promise<boolean>;
  getReviewVotes: (reviewId: string) => { helpful: number; unhelpful: number };
  getUserVote: (reviewId: string) => 'helpful' | 'unhelpful' | null;
  sortReviewsByHelpfulness: (reviews: Review[], ascending?: boolean) => Review[];
  getReviewAnalytics: () => ReviewAnalytics;

  // Utility functions
  isVerifiedPurchaser: (productId: string) => boolean;
  uploadReviewImages: (files: File[]) => Promise<string[]>;
}

const ReviewContext = createContext<ReviewContextType | undefined>(undefined);

export const ReviewProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();

  // State for reviews data
  const [productReviews, setProductReviews] = useState<Record<string, Review[]>>({});
  const [reviewSummaries, setReviewSummaries] = useState<Record<string, ReviewSummary>>({});
  const [pendingReviews, setPendingReviews] = useState<Review[]>([]);

  // Loading states
  const [isLoadingReviews, setIsLoadingReviews] = useState(false);
  const [isLoadingSummary, setIsLoadingSummary] = useState(false);
  const [isLoadingPendingReviews, setIsLoadingPendingReviews] = useState(false);

  // Get reviews for a product
  const getReviews = async (productId: string, status: ReviewStatus | 'all' = 'approved'): Promise<Review[]> => {
    setIsLoadingReviews(true);
    try {
      const reviews = getProductReviews(productId, status);

      // Update state
      setProductReviews(prev => ({
        ...prev,
        [productId]: reviews
      }));

      return reviews;
    } catch (error) {
      console.error('Error fetching reviews:', error);
      toast.error('Failed to load reviews');
      return [];
    } finally {
      setIsLoadingReviews(false);
    }
  };

  // Get filtered reviews for a product
  const getFilteredProductReviews = async (productId: string, options: ReviewFilterOptions): Promise<Review[]> => {
    setIsLoadingReviews(true);
    try {
      const reviews = getFilteredReviews(productId, options);
      return reviews;
    } catch (error) {
      console.error('Error fetching filtered reviews:', error);
      toast.error('Failed to load reviews');
      return [];
    } finally {
      setIsLoadingReviews(false);
    }
  };

  // Get review summary for a product
  const getSummary = async (productId: string): Promise<ReviewSummary> => {
    setIsLoadingSummary(true);
    try {
      const summary = getReviewSummary(productId);

      // Update state
      setReviewSummaries(prev => ({
        ...prev,
        [productId]: summary
      }));

      return summary;
    } catch (error) {
      console.error('Error fetching review summary:', error);
      toast.error('Failed to load review summary');
      return {
        productId,
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      };
    } finally {
      setIsLoadingSummary(false);
    }
  };

  // Submit a new review
  const submitReview = async (review: Omit<Review, 'id' | 'userId' | 'userName' | 'createdAt' | 'updatedAt' | 'isVerifiedPurchase' | 'status'>): Promise<Review> => {
    if (!isAuthenticated || !user) {
      toast.error('You must be logged in to submit a review');
      throw new Error('Not authenticated');
    }

    try {
      // Check if user has purchased the product
      const isVerifiedPurchase = hasUserPurchasedProduct(user.sub, review.productId);

      // Analyze sentiment
      const sentiment = analyzeSentiment(review.content);

      // Check for moderation flags
      const moderationFlags = moderateContent(review.content + ' ' + review.title);

      // Create the review with enhanced features
      const newReview = await addReviewService({
        ...review,
        userId: user.sub,
        userName: user.name || user.email || 'Anonymous',
        isVerifiedPurchase,
        status: 'pending', // All reviews start as pending for moderation
        helpfulVotes: 0,
        unhelpfulVotes: 0,
        userVotes: {},
        sentiment,
        moderationFlags: moderationFlags.length > 0 ? moderationFlags : undefined
      });

      // Log review submission
      logger.userAction('review_submitted', {
        reviewId: newReview.id,
        productId: review.productId,
        rating: review.rating,
        hasImages: review.images && review.images.length > 0,
        sentiment: sentiment.label,
        moderationFlags: moderationFlags.length,
        isVerifiedPurchase
      });

      // Refresh reviews for this product
      await refreshReviews(review.productId);

      toast.success('Your review has been submitted and is pending approval');
      return newReview;
    } catch (error) {
      console.error('Error submitting review:', error);
      logger.error('review_submission_failed', error);
      toast.error('Failed to submit review');
      throw error;
    }
  };

  // Update an existing review
  const updateReview = async (reviewId: string, updates: Partial<Review>): Promise<Review> => {
    try {
      const updatedReview = updateReviewService(reviewId, updates);

      // If the review status changed to approved, refresh the product reviews
      if (updates.status === 'approved' && updatedReview.productId) {
        await refreshReviews(updatedReview.productId);
      }

      // If this is a pending review and we're updating it, refresh pending reviews
      if (pendingReviews.some(r => r.id === reviewId)) {
        await refreshPendingReviews();
      }

      return updatedReview;
    } catch (error) {
      console.error('Error updating review:', error);
      toast.error('Failed to update review');
      throw error;
    }
  };

  // Delete a review
  const deleteReview = async (reviewId: string): Promise<boolean> => {
    try {
      // Find the review to get its productId before deletion
      const reviewToDelete = pendingReviews.find(r => r.id === reviewId) ||
        Object.values(productReviews).flat().find(r => r.id === reviewId);

      if (!reviewToDelete) {
        throw new Error('Review not found');
      }

      const productId = reviewToDelete.productId;

      // Delete the review
      const success = deleteReviewService(reviewId);

      if (success) {
        // Refresh reviews for this product
        await refreshReviews(productId);

        // Refresh pending reviews if needed
        if (pendingReviews.some(r => r.id === reviewId)) {
          await refreshPendingReviews();
        }

        toast.success('Review deleted successfully');
      }

      return success;
    } catch (error) {
      console.error('Error deleting review:', error);
      toast.error('Failed to delete review');
      return false;
    }
  };

  // Add admin response to a review
  const addAdminResponse = async (reviewId: string, response: string): Promise<Review> => {
    if (!isAuthenticated || !user) {
      toast.error('You must be logged in as an admin to respond to reviews');
      throw new Error('Not authenticated');
    }

    try {
      const updatedReview = addAdminResponseService(reviewId, response, user.sub);

      // Refresh reviews for this product
      if (updatedReview.productId) {
        await refreshReviews(updatedReview.productId);
      }

      // Refresh pending reviews if needed
      if (pendingReviews.some(r => r.id === reviewId)) {
        await refreshPendingReviews();
      }

      toast.success('Response added successfully');
      return updatedReview;
    } catch (error) {
      console.error('Error adding admin response:', error);
      toast.error('Failed to add response');
      throw error;
    }
  };

  // Get pending reviews for admin moderation
  const getPendingReviews = async (): Promise<Review[]> => {
    setIsLoadingPendingReviews(true);
    try {
      const reviews = getPendingReviewsService();
      setPendingReviews(reviews);
      return reviews;
    } catch (error) {
      console.error('Error fetching pending reviews:', error);
      toast.error('Failed to load pending reviews');
      return [];
    } finally {
      setIsLoadingPendingReviews(false);
    }
  };

  // Refresh reviews for a product
  const refreshReviews = async (productId: string): Promise<void> => {
    await getReviews(productId);
    await getSummary(productId);
  };

  // Refresh pending reviews
  const refreshPendingReviews = async (): Promise<void> => {
    setIsLoadingPendingReviews(true);
    try {
      const reviews = getPendingReviewsService();
      // Update state directly without triggering the full getPendingReviews function
      setPendingReviews(reviews);
    } catch (error) {
      console.error('Error refreshing pending reviews:', error);
    } finally {
      setIsLoadingPendingReviews(false);
    }
  };

  // Check if the current user is a verified purchaser of a product
  const isVerifiedPurchaser = (productId: string): boolean => {
    if (!isAuthenticated || !user) return false;
    return hasUserPurchasedProduct(user.sub, productId);
  };

  // Upload review images
  const uploadReviewImages = async (files: File[]): Promise<string[]> => {
    try {
      // In a real app, you would upload these to a server/cloud storage
      // For demo purposes, we'll create object URLs
      const imageUrls = files.map(file => URL.createObjectURL(file));
      return imageUrls;
    } catch (error) {
      console.error('Error uploading images:', error);
      toast.error('Failed to upload images');
      return [];
    }
  };

  // Enhanced voting functionality
  const handleVoteOnReview = async (reviewId: string, voteType: 'helpful' | 'unhelpful'): Promise<boolean> => {
    if (!isAuthenticated || !user) {
      toast.error('Please log in to vote on reviews');
      return false;
    }

    try {
      const result = voteOnReview(reviewId, user.sub, voteType);
      if (result.success) {
        logger.userAction('review_voted', {
          reviewId,
          voteType,
          userId: user.sub
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error voting on review:', error);
      logger.error('review_vote_failed', error);
      return false;
    }
  };

  // Get review vote counts
  const getReviewVotes = (reviewId: string) => {
    return getReviewVoteCounts(reviewId);
  };

  // Get user's vote for a review
  const getUserVote = (reviewId: string) => {
    if (!isAuthenticated || !user) return null;
    return getUserVoteForReview(reviewId, user.sub);
  };

  // Sort reviews by helpfulness
  const sortByHelpfulness = (reviews: Review[], ascending: boolean = false) => {
    return sortReviewsByHelpfulness(reviews, ascending);
  };

  // Get comprehensive analytics
  const getAnalytics = (): ReviewAnalytics => {
    const allReviews = Object.values(productReviews).flat();
    return getReviewAnalytics(allReviews);
  };

  // Load pending reviews for admins on mount
  useEffect(() => {
    if (isAuthenticated && user?.role === 'admin') {
      getPendingReviews();
    }
  }, [isAuthenticated, user]);

  return (
    <ReviewContext.Provider
      value={{
        productReviews,
        reviewSummaries,
        pendingReviews,
        isLoadingReviews,
        isLoadingSummary,
        isLoadingPendingReviews,
        getReviews,
        getFilteredProductReviews,
        getSummary,
        submitReview,
        updateReview,
        deleteReview,
        addAdminResponse,
        getPendingReviews,
        refreshReviews,
        refreshPendingReviews,
        // Enhanced features
        voteOnReview: handleVoteOnReview,
        getReviewVotes,
        getUserVote,
        sortReviewsByHelpfulness: sortByHelpfulness,
        getReviewAnalytics: getAnalytics,
        // Utility functions
        isVerifiedPurchaser,
        uploadReviewImages
      }}
    >
      {children}
    </ReviewContext.Provider>
  );
};

export const useReviews = () => {
  const context = useContext(ReviewContext);
  if (context === undefined) {
    throw new Error('useReviews must be used within a ReviewProvider');
  }
  return context;
};

export default ReviewContext;
