import React, { useState, useEffect } from "react";
import { useInventory } from "@/contexts/InventoryContext";
import { useAuth } from "@/contexts/AuthContext";
import { InventoryItem } from "@/types/inventory";
import AdvancedInventoryManagement from "./AdvancedInventoryManagement";
import StockAlerts from "./StockAlerts";
import InventoryForecasting from "./InventoryForecasting";
import RecommendationAnalytics from "./RecommendationAnalytics";
import ReviewAnalyticsDashboard from "./ReviewAnalyticsDashboard";
import AdvancedAnalyticsDashboard from "./AdvancedAnalyticsDashboard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Ta<PERSON>, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  AreaChart,
  Area
} from "recharts";
import {
  Package,
  AlertTriangle,
  DollarSign,
  Users,
  TrendingUp,
  Activity,
  ShoppingBag,
  RefreshCw,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  CreditCard,
  Truck,
  Star,
  BarChart3,
  PieChart as PieChartIcon,
  Percent
} from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { toast } from "sonner";
import { format, subDays } from "date-fns";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";

// Helper function to generate dates
const getDate = (daysAgo) => {
  return format(subDays(new Date(), daysAgo), 'yyyy-MM-dd');
};

// Mock data for the dashboard
const mockSalesData = [
  { name: 'Jan', sales: 4000, orders: 120, profit: 1600, customers: 85, conversion: 3.2 },
  { name: 'Feb', sales: 3000, orders: 90, profit: 1200, customers: 65, conversion: 2.8 },
  { name: 'Mar', sales: 5000, orders: 150, profit: 2000, customers: 110, conversion: 3.5 },
  { name: 'Apr', sales: 2780, orders: 85, profit: 1100, customers: 60, conversion: 2.5 },
  { name: 'May', sales: 1890, orders: 60, profit: 750, customers: 45, conversion: 2.2 },
  { name: 'Jun', sales: 2390, orders: 70, profit: 950, customers: 55, conversion: 2.4 },
  { name: 'Jul', sales: 3490, orders: 105, profit: 1400, customers: 80, conversion: 2.9 },
  { name: 'Aug', sales: 3890, orders: 115, profit: 1550, customers: 90, conversion: 3.1 },
  { name: 'Sep', sales: 4200, orders: 125, profit: 1680, customers: 95, conversion: 3.3 },
  { name: 'Oct', sales: 4500, orders: 135, profit: 1800, customers: 100, conversion: 3.4 },
  { name: 'Nov', sales: 5100, orders: 155, profit: 2050, customers: 115, conversion: 3.6 },
  { name: 'Dec', sales: 6200, orders: 185, profit: 2480, customers: 140, conversion: 3.8 },
];

const mockProductData = [
  { name: 'Soaps', value: 400, percentage: 44, revenue: 12000, growth: 8.5 },
  { name: 'Shampoos', value: 300, percentage: 33, revenue: 15000, growth: 12.3 },
  { name: 'Bundles', value: 200, percentage: 23, revenue: 9000, growth: 15.7 },
];

const mockDailyData = [
  { name: '12 AM', value: 5, visitors: 42, conversion: 1.2 },
  { name: '3 AM', value: 2, visitors: 18, conversion: 0.8 },
  { name: '6 AM', value: 8, visitors: 65, conversion: 1.5 },
  { name: '9 AM', value: 15, visitors: 120, conversion: 2.1 },
  { name: '12 PM', value: 25, visitors: 180, conversion: 2.8 },
  { name: '3 PM', value: 30, visitors: 210, conversion: 3.2 },
  { name: '6 PM', value: 22, visitors: 175, conversion: 2.5 },
  { name: '9 PM', value: 12, visitors: 95, conversion: 1.8 },
];

const mockPaymentMethodData = [
  { name: 'Credit Card', value: 65 },
  { name: 'PayPal', value: 25 },
  { name: 'Other', value: 10 },
];

const mockRecentOrders = [
  {
    id: 'ORD-001',
    customer: {
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: null,
      isReturning: true
    },
    date: getDate(1),
    status: 'delivered',
    total: 89.97,
    items: 3,
    paymentMethod: 'credit_card',
    shippingMethod: 'standard'
  },
  {
    id: 'ORD-002',
    customer: {
      name: 'Jane Smith',
      email: '<EMAIL>',
      avatar: null,
      isReturning: true
    },
    date: getDate(2),
    status: 'processing',
    total: 124.95,
    items: 2,
    paymentMethod: 'paypal',
    shippingMethod: 'express'
  },
  {
    id: 'ORD-003',
    customer: {
      name: 'Robert Johnson',
      email: '<EMAIL>',
      avatar: null,
      isReturning: false
    },
    date: getDate(3),
    status: 'shipped',
    total: 59.99,
    items: 1,
    paymentMethod: 'credit_card',
    shippingMethod: 'standard'
  },
  {
    id: 'ORD-004',
    customer: {
      name: 'Emily Davis',
      email: '<EMAIL>',
      avatar: null,
      isReturning: false
    },
    date: getDate(3),
    status: 'pending',
    total: 149.97,
    items: 4,
    paymentMethod: 'credit_card',
    shippingMethod: 'express'
  },
  {
    id: 'ORD-005',
    customer: {
      name: 'Michael Wilson',
      email: '<EMAIL>',
      avatar: null,
      isReturning: true
    },
    date: getDate(4),
    status: 'processing',
    total: 79.98,
    items: 2,
    paymentMethod: 'paypal',
    shippingMethod: 'standard'
  }
];

const mockActivityFeed = [
  {
    id: 1,
    type: 'order',
    message: 'New order placed',
    details: 'Order #ORD-005 placed by Michael Wilson',
    time: '10 minutes ago',
    timestamp: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
    icon: ShoppingBag,
    color: 'blue'
  },
  {
    id: 2,
    type: 'inventory',
    message: 'Low stock alert',
    details: 'Lemon Verbena Fresh Bar is running low (3 left)',
    time: '25 minutes ago',
    timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
    icon: AlertTriangle,
    color: 'amber'
  },
  {
    id: 3,
    type: 'review',
    message: 'New review submitted',
    details: 'Jane Smith left a 5-star review for Scalp Rescue Shampoo',
    time: '1 hour ago',
    timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
    icon: Star,
    color: 'green'
  },
  {
    id: 4,
    type: 'order',
    message: 'Order status updated',
    details: 'Order #ORD-002 status changed to Processing',
    time: '2 hours ago',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    icon: Truck,
    color: 'blue'
  },
  {
    id: 5,
    type: 'inventory',
    message: 'Product restocked',
    details: 'Rose Clay Glow Bar restocked with 20 units',
    time: '3 hours ago',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
    icon: Package,
    color: 'green'
  },
  {
    id: 6,
    type: 'user',
    message: 'New customer registered',
    details: 'Emily Davis created a new account',
    time: '5 hours ago',
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
    icon: Users,
    color: 'purple'
  },
  {
    id: 7,
    type: 'payment',
    message: 'Payment received',
    details: 'Payment for order #ORD-003 was successful',
    time: '6 hours ago',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
    icon: CreditCard,
    color: 'green'
  }
];

const COLORS = ['#C6A870', '#8A7A5A', '#5A4A3A'];

const STATUS_COLORS = {
  pending: 'bg-yellow-500/20 text-yellow-500 hover:bg-yellow-500/30 border-yellow-500/30',
  processing: 'bg-blue-500/20 text-blue-500 hover:bg-blue-500/30 border-blue-500/30',
  shipped: 'bg-indigo-500/20 text-indigo-500 hover:bg-indigo-500/30 border-indigo-500/30',
  delivered: 'bg-green-500/20 text-green-500 hover:bg-green-500/30 border-green-500/30',
  cancelled: 'bg-red-500/20 text-red-500 hover:bg-red-500/30 border-red-500/30',
};

const ACTIVITY_COLORS = {
  blue: 'bg-blue-500/20 text-blue-500',
  green: 'bg-green-500/20 text-green-500',
  amber: 'bg-amber-500/20 text-amber-500',
  red: 'bg-red-500/20 text-red-500',
  purple: 'bg-purple-500/20 text-purple-500',
  gold: 'bg-sabone-gold/20 text-sabone-gold',
};

const Dashboard = () => {
  const { lowStockItems, refreshInventory } = useInventory();
  const { _user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('year'); // 'day', 'week', 'month', 'year'
  const [chartView, setChartView] = useState('area'); // 'area', 'bar', 'line'
  const [salesData, _setSalesData] = useState(mockSalesData);
  const [recentOrders, _setRecentOrders] = useState(mockRecentOrders);
  const [activityFeed, _setActivityFeed] = useState(mockActivityFeed);
  const [paymentMethodData, _setPaymentMethodData] = useState(mockPaymentMethodData);
  const [_dailyData, _setDailyData] = useState(mockDailyData);
  const [productData, _setProductData] = useState(mockProductData);
  const [lowStockAlerts, setLowStockAlerts] = useState<InventoryItem[]>([]);
  const [dataLoadError, setDataLoadError] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const _isMobile = useIsMobile();

  // Calculate summary metrics
  const totalRevenue = salesData.reduce((sum, item) => sum + item.sales, 0);
  const totalOrders = salesData.reduce((sum, item) => sum + item.orders, 0);
  const _totalProfit = salesData.reduce((sum, item) => sum + item.profit, 0);
  const totalCustomers = salesData.reduce((sum, item) => sum + item.customers, 0);
  const averageOrderValue = totalRevenue / totalOrders;
  const conversionRate = salesData.reduce((sum, item) => sum + item.conversion, 0) / salesData.length;

  // Calculate month-over-month growth
  const currentMonth = salesData[salesData.length - 1];
  const previousMonth = salesData[salesData.length - 2];
  const revenueGrowth = ((currentMonth.sales - previousMonth.sales) / previousMonth.sales) * 100;
  const ordersGrowth = ((currentMonth.orders - previousMonth.orders) / previousMonth.orders) * 100;
  const _profitGrowth = ((currentMonth.profit - previousMonth.profit) / previousMonth.profit) * 100;
  const customersGrowth = ((currentMonth.customers - previousMonth.customers) / previousMonth.customers) * 100;

  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value);
  };

  // Get status badge class
  const _getStatusBadgeClass = (status) => {
    return STATUS_COLORS[status] || 'bg-gray-500/20 text-gray-500 hover:bg-gray-500/30 border-gray-500/30';
  };

  // Get activity icon class
  const getActivityIconClass = (color) => {
    return ACTIVITY_COLORS[color] || ACTIVITY_COLORS.gold;
  };

  // Use a ref to track if this is the initial mount
  const initialMountRef = React.useRef(true);
  // Store the current refreshInventory function in a ref to avoid dependency issues
  const refreshInventoryRef = React.useRef(refreshInventory);
  
  // Update the ref when refreshInventory changes
  useEffect(() => {
    refreshInventoryRef.current = refreshInventory;
  }, [refreshInventory]);
  
  // Single effect to handle the initial load
  useEffect(() => {
    // Only run this effect once on mount
    if (initialMountRef.current) {
      const loadInitialData = async () => {
        setIsLoading(true);
        setDataLoadError(false);
        
        try {
          // Call refreshInventory via the ref to avoid dependency issues
          await refreshInventoryRef.current();
          
          // Simulate API call delay for loading indicator
          setTimeout(() => {
            setIsLoading(false);
          }, 1000);
          
          // Mark initial loading as complete
          initialMountRef.current = false;
        } catch (error) {
          // Log error but avoid using console in production
          /* eslint-disable-next-line no-console */
          if (process.env.NODE_ENV !== 'production') {
            console.error('Error loading dashboard data:', error);
          }
          setDataLoadError(true);
          setIsLoading(false);
          toast.error('Failed to load dashboard data. Please try again.');
        }
      };
      
      loadInitialData();
    }
  }, []); // Empty dependency array - intentional as we're using refs
  
  // Separate effect to update local state when context data changes
  useEffect(() => {
    // Skip on initial render since we'll load data in the first effect
    if (!initialMountRef.current && lowStockItems) {
      if (lowStockItems.length > 0) {
        // Explicitly cast to InventoryItem[] to ensure type safety
        setLowStockAlerts(lowStockItems as InventoryItem[]);
      } else {
        setLowStockAlerts([]);
      }
    }
  }, [lowStockItems]); // Only depend on lowStockItems

  // Function to handle manual refresh
  const handleRefresh = async () => {
    // Prevent refresh during initial load
    if (initialMountRef.current) return;
    
    setIsLoading(true);
    setDataLoadError(false);

    try {
      // Use the ref to refreshInventory to maintain consistency
      await refreshInventoryRef.current();
      // Don't update lowStockAlerts here - let the useEffect handle that
      
      // Simulate API call delay
      setTimeout(() => {
        setIsLoading(false);
        toast.success('Dashboard data refreshed successfully');
      }, 1000);
    } catch (error) {
      /* eslint-disable-next-line no-console */
      if (process.env.NODE_ENV !== 'production') {
        console.error('Error refreshing dashboard data:', error);
      }
      setDataLoadError(true);
      setIsLoading(false);
      toast.error('Failed to refresh dashboard data. Please try again.');
    }
  };

  // Function to handle chart view change
  const handleChartViewChange = (view) => {
    setChartView(view);
  };

  // Function to get status badge
  const getStatusBadge = (status) => {
    return (
      <Badge variant="outline" className={STATUS_COLORS[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-playfair font-semibold text-sabone-gold">Dashboard Overview</h2>
          <p className="text-sabone-cream/70">Welcome to the Sabone admin dashboard. Here's an overview of your store's performance.</p>
        </div>

        <div className="flex gap-2">
          {dataLoadError && (
            <Button
              variant="outline"
              className="border-red-500/30 text-red-500 hover:bg-red-500/10"
              onClick={handleRefresh}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}
          <Button
            variant="outline"
            className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh Data
          </Button>
        </div>
      </div>

      <Separator className="bg-sabone-gold/20 my-6" />

      {/* Error State */}
      {dataLoadError && (
        <Card className="bg-red-500/10 border-red-500/30">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-6 w-6 text-red-500" />
              <div>
                <h3 className="text-lg font-semibold text-red-500">Failed to load dashboard data</h3>
                <p className="text-sabone-cream/70">There was an error loading the dashboard data. Please try refreshing.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dashboard Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7 bg-sabone-dark-olive/60">
          <TabsTrigger value="overview" className="data-[state=active]:bg-sabone-gold/20">
            Overview
          </TabsTrigger>
          <TabsTrigger value="analytics" className="data-[state=active]:bg-sabone-gold/20">
            Analytics
          </TabsTrigger>
          <TabsTrigger value="inventory" className="data-[state=active]:bg-sabone-gold/20">
            Inventory
          </TabsTrigger>
          <TabsTrigger value="alerts" className="data-[state=active]:bg-sabone-gold/20">
            Alerts
          </TabsTrigger>
          <TabsTrigger value="forecasting" className="data-[state=active]:bg-sabone-gold/20">
            Forecasting
          </TabsTrigger>
          <TabsTrigger value="recommendations" className="data-[state=active]:bg-sabone-gold/20">
            Recommendations
          </TabsTrigger>
          <TabsTrigger value="reviews" className="data-[state=active]:bg-sabone-gold/20">
            Reviews
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Revenue Card */}
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sabone-gold text-lg flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-24 bg-sabone-gold/10" />
                <Skeleton className="h-4 w-32 bg-sabone-gold/10" />
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold text-sabone-cream">{formatCurrency(totalRevenue)}</div>
                <div className="flex items-center text-xs mt-1">
                  {revenueGrowth >= 0 ? (
                    <span className="text-green-500 flex items-center">
                      <ArrowUpRight className="h-3 w-3 mr-1" />
                      +{revenueGrowth.toFixed(1)}%
                    </span>
                  ) : (
                    <span className="text-red-500 flex items-center">
                      <ArrowDownRight className="h-3 w-3 mr-1" />
                      {revenueGrowth.toFixed(1)}%
                    </span>
                  )}
                  <span className="text-sabone-cream/70 ml-2">from last month</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Orders Card */}
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sabone-gold text-lg flex items-center">
              <ShoppingBag className="h-5 w-5 mr-2" />
              Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-16 bg-sabone-gold/10" />
                <Skeleton className="h-4 w-32 bg-sabone-gold/10" />
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold text-sabone-cream">{totalOrders}</div>
                <div className="flex items-center text-xs mt-1">
                  {ordersGrowth >= 0 ? (
                    <span className="text-green-500 flex items-center">
                      <ArrowUpRight className="h-3 w-3 mr-1" />
                      +{ordersGrowth.toFixed(1)}%
                    </span>
                  ) : (
                    <span className="text-red-500 flex items-center">
                      <ArrowDownRight className="h-3 w-3 mr-1" />
                      {ordersGrowth.toFixed(1)}%
                    </span>
                  )}
                  <span className="text-sabone-cream/70 ml-2">from last month</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Customers Card */}
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sabone-gold text-lg flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Customers
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-16 bg-sabone-gold/10" />
                <Skeleton className="h-4 w-32 bg-sabone-gold/10" />
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold text-sabone-cream">{totalCustomers}</div>
                <div className="flex items-center text-xs mt-1">
                  {customersGrowth >= 0 ? (
                    <span className="text-green-500 flex items-center">
                      <ArrowUpRight className="h-3 w-3 mr-1" />
                      +{customersGrowth.toFixed(1)}%
                    </span>
                  ) : (
                    <span className="text-red-500 flex items-center">
                      <ArrowDownRight className="h-3 w-3 mr-1" />
                      {customersGrowth.toFixed(1)}%
                    </span>
                  )}
                  <span className="text-sabone-cream/70 ml-2">from last month</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Low Stock Card */}
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sabone-gold text-lg flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Low Stock
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-16 bg-sabone-gold/10" />
                <Skeleton className="h-4 w-32 bg-sabone-gold/10" />
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold text-sabone-cream">{lowStockAlerts.length}</div>
                <div className="flex items-center text-xs mt-1">
                  <span className="text-sabone-cream/70">products need attention</span>
                </div>
                {lowStockAlerts.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 border-amber-500/30 text-amber-500 hover:bg-amber-500/10"
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    View Items
                  </Button>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Average Order Value */}
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sabone-gold text-lg flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              Avg. Order Value
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-24 bg-sabone-gold/10" />
            ) : (
              <div className="text-2xl font-bold text-sabone-cream">{formatCurrency(averageOrderValue)}</div>
            )}
          </CardContent>
        </Card>

        {/* Conversion Rate */}
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sabone-gold text-lg flex items-center">
              <Percent className="h-5 w-5 mr-2" />
              Conversion Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16 bg-sabone-gold/10" />
            ) : (
              <div className="text-2xl font-bold text-sabone-cream">{conversionRate.toFixed(1)}%</div>
            )}
          </CardContent>
        </Card>

        {/* Payment Methods */}
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sabone-gold text-lg flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              Payment Methods
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-4 w-full bg-sabone-gold/10" />
                <Skeleton className="h-4 w-3/4 bg-sabone-gold/10" />
                <Skeleton className="h-4 w-1/2 bg-sabone-gold/10" />
              </div>
            ) : (
              <div className="space-y-2">
                {paymentMethodData.map((method) => (
                  <div key={method.name} className="flex items-center justify-between">
                    <span className="text-sabone-cream/80 text-sm">{method.name}</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sabone-cream text-sm font-medium">{method.value}%</span>
                      <Progress value={method.value} className="w-24 h-2 bg-sabone-gold/10" indicatorClassName="bg-sabone-gold" />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20 lg:col-span-2">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sabone-gold flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Sales Overview
            </CardTitle>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="flex space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  className={`border-sabone-gold/30 ${timeRange === 'month' ? 'bg-sabone-gold/20 text-sabone-gold' : 'text-sabone-cream/70 hover:text-sabone-gold'}`}
                  onClick={() => setTimeRange('month')}
                >
                  Month
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={`border-sabone-gold/30 ${timeRange === 'year' ? 'bg-sabone-gold/20 text-sabone-gold' : 'text-sabone-cream/70 hover:text-sabone-gold'}`}
                  onClick={() => setTimeRange('year')}
                >
                  Year
                </Button>
              </div>
              <div className="flex space-x-1">
                <Button
                  variant="outline"
                  size="sm"
                  className={`border-sabone-gold/30 ${chartView === 'area' ? 'bg-sabone-gold/20 text-sabone-gold' : 'text-sabone-cream/70 hover:text-sabone-gold'}`}
                  onClick={() => handleChartViewChange('area')}
                >
                  <AreaChart className="h-3 w-3 mr-1" />
                  Area
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={`border-sabone-gold/30 ${chartView === 'bar' ? 'bg-sabone-gold/20 text-sabone-gold' : 'text-sabone-cream/70 hover:text-sabone-gold'}`}
                  onClick={() => handleChartViewChange('bar')}
                >
                  <BarChart3 className="h-3 w-3 mr-1" />
                  Bar
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <div className="text-center">
                  <RefreshCw className="h-8 w-8 text-sabone-gold/40 animate-spin mx-auto mb-4" />
                  <p className="text-sabone-cream/50">Loading chart data...</p>
                </div>
              </div>
            ) : (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  {chartView === 'area' ? (
                    <AreaChart
                      data={salesData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                      <XAxis dataKey="name" stroke="#E5DCC5" />
                      <YAxis stroke="#E5DCC5" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#2A2A1F',
                          borderColor: '#C6A870',
                          color: '#E5DCC5'
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="sales"
                        stroke="#C6A870"
                        fill="#C6A870"
                        fillOpacity={0.3}
                        name="Revenue"
                      />
                      <Area
                        type="monotone"
                        dataKey="profit"
                        stroke="#8A7A5A"
                        fill="#8A7A5A"
                        fillOpacity={0.3}
                        name="Profit"
                      />
                      <Legend />
                    </AreaChart>
                  ) : (
                    <BarChart
                      data={salesData}
                      margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                      <XAxis dataKey="name" stroke="#E5DCC5" />
                      <YAxis stroke="#E5DCC5" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#2A2A1F',
                          borderColor: '#C6A870',
                          color: '#E5DCC5'
                        }}
                      />
                      <Bar dataKey="sales" fill="#C6A870" name="Revenue" />
                      <Bar dataKey="profit" fill="#8A7A5A" name="Profit" />
                      <Legend />
                    </BarChart>
                  )}
                </ResponsiveContainer>
              </div>
            )}
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sabone-gold flex items-center">
              <PieChartIcon className="h-5 w-5 mr-2" />
              Product Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="h-80 flex items-center justify-center">
                <div className="text-center">
                  <RefreshCw className="h-8 w-8 text-sabone-gold/40 animate-spin mx-auto mb-4" />
                  <p className="text-sabone-cream/50">Loading chart data...</p>
                </div>
              </div>
            ) : (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={productData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {productData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      contentStyle={{
                        backgroundColor: '#2A2A1F',
                        borderColor: '#C6A870',
                        color: '#E5DCC5'
                      }}
                      formatter={(value, name) => [`${value} (${productData.find(item => item.name === name)?.growth > 0 ? '+' : ''}${productData.find(item => item.name === name)?.growth}%)`, name]}
                    />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
            <div className="mt-4 space-y-2">
              {!isLoading && productData.map((product, index) => (
                <div key={product.name} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: COLORS[index % COLORS.length] }} />
                    <span className="text-sabone-cream/80 text-sm">{product.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sabone-cream text-sm font-medium">{formatCurrency(product.revenue)}</span>
                    <span className={product.growth >= 0 ? 'text-green-500 text-xs' : 'text-red-500 text-xs'}>
                      {product.growth >= 0 ? '+' : ''}{product.growth}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders and Activity Feed */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
        {/* Recent Orders */}
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sabone-gold flex items-center">
              <ShoppingBag className="h-5 w-5 mr-2" />
              Recent Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between py-2 border-b border-sabone-gold/10 last:border-0">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-8 w-8 rounded-full bg-sabone-gold/10" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-24 bg-sabone-gold/10" />
                        <Skeleton className="h-3 w-16 bg-sabone-gold/10" />
                      </div>
                    </div>
                    <Skeleton className="h-6 w-16 rounded-md bg-sabone-gold/10" />
                  </div>
                ))}
              </div>
            ) : (
              <>
                <div className="rounded-md border border-sabone-gold/20 overflow-hidden">
                  <Table>
                    <TableHeader className="bg-sabone-dark-olive/60">
                      <TableRow>
                        <TableHead className="text-sabone-gold">Order</TableHead>
                        <TableHead className="text-sabone-gold">Customer</TableHead>
                        <TableHead className="text-sabone-gold text-right">Amount</TableHead>
                        <TableHead className="text-sabone-gold text-center">Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {recentOrders.slice(0, 5).map((order) => (
                        <TableRow key={order.id} className="hover:bg-sabone-dark-olive/30">
                          <TableCell className="font-medium text-sabone-gold">{order.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Avatar className="h-6 w-6 mr-2">
                                <AvatarImage src={order.customer.avatar || ''} alt={order.customer.name} />
                                <AvatarFallback className="bg-sabone-dark-olive text-sabone-gold text-xs">
                                  {order.customer.name.split(' ').map(n => n[0]).join('')}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <span className="text-sabone-cream text-sm block">{order.customer.name}</span>
                                {order.customer.isReturning && (
                                  <span className="text-sabone-cream/50 text-xs">Returning customer</span>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-sabone-cream text-right">{formatCurrency(order.total)}</TableCell>
                          <TableCell className="text-center">
                            {getStatusBadge(order.status)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
                <div className="mt-4 text-center">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    onClick={() => window.location.href = '/account/admin/orders'}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View All Orders
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Activity Feed */}
        <Card className="bg-sabone-dark-olive/60 border-sabone-gold/20">
          <CardHeader className="pb-2">
            <CardTitle className="text-sabone-gold flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-start pb-4 border-b border-sabone-gold/10 last:border-0 last:pb-0">
                    <Skeleton className="h-8 w-8 rounded-full bg-sabone-gold/10 mr-4" />
                    <div className="flex-1 space-y-2">
                      <div className="flex justify-between">
                        <Skeleton className="h-4 w-24 bg-sabone-gold/10" />
                        <Skeleton className="h-3 w-16 bg-sabone-gold/10" />
                      </div>
                      <Skeleton className="h-3 w-full bg-sabone-gold/10" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {activityFeed.map((activity) => (
                  <div key={activity.id} className="flex items-start pb-4 border-b border-sabone-gold/10 last:border-0 last:pb-0">
                    <div className="mr-4 mt-1">
                      <div className={`h-8 w-8 rounded-full ${getActivityIconClass(activity.color)} flex items-center justify-center`}>
                        {React.createElement(activity.icon, { className: "h-4 w-4" })}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <h4 className="text-sabone-gold font-medium text-sm">{activity.message}</h4>
                        <span className="text-sabone-cream/50 text-xs flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {activity.time}
                        </span>
                      </div>
                      <p className="text-sabone-cream/70 text-sm mt-1">{activity.details}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
            <div className="mt-4 text-center">
              <Button
                variant="outline"
                size="sm"
                className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
              >
                <Eye className="h-4 w-4 mr-2" />
                View All Activity
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AdvancedAnalyticsDashboard />
        </TabsContent>

        <TabsContent value="inventory" className="space-y-6">
          <AdvancedInventoryManagement />
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <StockAlerts />
        </TabsContent>

        <TabsContent value="forecasting" className="space-y-6">
          <InventoryForecasting />
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <RecommendationAnalytics />
        </TabsContent>

        <TabsContent value="reviews" className="space-y-6">
          <ReviewAnalyticsDashboard />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
